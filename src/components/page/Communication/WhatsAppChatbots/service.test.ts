import * as api from '../../../../services/api';
import {
  getConnectedAccounts,
  getEntityFields,
  createChatbotStep1,
  submitChatbotQuestions,
  uploadKnowledgeBase,
  publishChatbot
} from './service';

// Mock the API module
jest.mock('../../../../services/api', () => ({
  setHeaders: jest.fn(),
  callAPI: jest.fn()
}));

jest.mock('../../../../config/apiConfig', () => ({
  defaultApiConfig: {},
  generateBaseUrl: jest.fn(() => 'http://localhost:3000/v1')
}));

const mockedApi = api as jest.Mocked<typeof api>;

describe('WhatsApp Chatbot Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedApi.setHeaders.mockReturnValue({
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token'
    });
  });

  describe('getConnectedAccounts', () => {
    const mockApiResponse = {
      data: {
        content: [
          {
            id: 6,
            wabaNumber: "+************",
            displayName: "Kalpesh QA Rename",
            name: "Kalpesh QA",
            entitiesToCreate: [],
            status: "active",
            isVerified: true
          },
          {
            id: 5,
            wabaNumber: "***********",
            displayName: "Abhinav newq",
            name: "Abhinav new",
            entitiesToCreate: ["lead", "contact"],
            status: "active",
            isVerified: true
          },
          {
            id: 3,
            wabaNumber: "+************",
            displayName: "support new name 786",
            name: "Kylas Shastri",
            entitiesToCreate: ["lead"],
            status: "inactive",
            isVerified: true
          }
        ]
      }
    };

    it('should fetch and flatten connected accounts correctly', async () => {
      mockedApi.callAPI.mockResolvedValue(mockApiResponse);

      const result = await getConnectedAccounts();

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/messages/connected-accounts',
        headers: expect.any(Object),
        method: 'get'
      });

      // Should filter only active accounts and flatten based on entitiesToCreate
      expect(result).toHaveLength(3); // 1 from first account (default to lead), 2 from second account (lead + contact)

      // Check first account (defaults to lead)
      expect(result[0]).toEqual({
        id: 6,
        wabaNumber: "+************",
        displayName: "Kalpesh QA Rename",
        name: "Kalpesh QA",
        entityType: "lead",
        status: "active",
        isVerified: true,
        originalAccount: mockApiResponse.data.content[0]
      });

      // Check second account (has both lead and contact)
      expect(result[1].entityType).toBe("lead");
      expect(result[2].entityType).toBe("contact");
      expect(result[1].id).toBe(5);
      expect(result[2].id).toBe(5);
    });

    it('should create multiple rows for accounts with multiple entitiesToCreate values', async () => {
      // Mock account with 3 entity types
      const mockResponseWithMultipleEntities = {
        data: {
          content: [
            {
              id: 10,
              wabaNumber: "+**********",
              displayName: "Multi Entity Account",
              name: "Test Account",
              entitiesToCreate: ["lead", "contact", "deal"],
              status: "active",
              isVerified: true
            }
          ]
        }
      };

      mockedApi.callAPI.mockResolvedValue(mockResponseWithMultipleEntities);

      const result = await getConnectedAccounts();

      // Should create 3 separate rows for the same account
      expect(result).toHaveLength(3);

      // All rows should have same account info but different entity types
      expect(result[0]).toEqual({
        id: 10,
        wabaNumber: "+**********",
        displayName: "Multi Entity Account",
        name: "Test Account",
        entityType: "lead",
        status: "active",
        isVerified: true,
        originalAccount: mockResponseWithMultipleEntities.data.content[0]
      });

      expect(result[1]).toEqual({
        id: 10,
        wabaNumber: "+**********",
        displayName: "Multi Entity Account",
        name: "Test Account",
        entityType: "contact",
        status: "active",
        isVerified: true,
        originalAccount: mockResponseWithMultipleEntities.data.content[0]
      });

      expect(result[2]).toEqual({
        id: 10,
        wabaNumber: "+**********",
        displayName: "Multi Entity Account",
        name: "Test Account",
        entityType: "deal",
        status: "active",
        isVerified: true,
        originalAccount: mockResponseWithMultipleEntities.data.content[0]
      });
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      mockedApi.callAPI.mockRejectedValue(error);

      await expect(getConnectedAccounts()).rejects.toThrow('API Error');
    });

    it('should filter accounts requiring both active status and non-empty entitiesToCreate', async () => {
      const mockResponseWithFiltering = {
        data: {
          content: [
            {
              id: 6,
              wabaNumber: "+************",
              displayName: "Kalpesh QA Rename",
              name: "Kalpesh QA",
              entitiesToCreate: [], // Empty - should be filtered out
              status: "active",
              isVerified: true
            },
            {
              id: 5,
              wabaNumber: "***********",
              displayName: "Abhinav newq",
              name: "Abhinav new",
              entitiesToCreate: ["lead", "contact"], // Has entities but inactive - should be filtered out
              status: "inactive",
              isVerified: true
            },
            {
              id: 4,
              wabaNumber: "+************",
              displayName: "New Wa Ba",
              name: "Atharva New BusinessAccount",
              entitiesToCreate: ["contact"], // Active and has entities - should be included
              status: "active",
              isVerified: true
            }
          ]
        }
      };

      mockedApi.callAPI.mockResolvedValue(mockResponseWithFiltering);

      const result = await getConnectedAccounts();

      // Should only include the one account that is both active AND has non-empty entitiesToCreate
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 4,
        wabaNumber: "+************",
        displayName: "New Wa Ba",
        name: "Atharva New BusinessAccount",
        entityType: "contact",
        status: "active",
        isVerified: true,
        originalAccount: mockResponseWithFiltering.data.content[2]
      });
    });
  });

  describe('getEntityFields', () => {
    const mockFieldsResponse = {
      data: [
        {
          id: 1,
          displayName: 'First Name',
          description: 'Lead first name',
          type: 'TEXT_FIELD',
          name: 'firstName',
          internal: false
        },
        {
          id: 2,
          displayName: 'Internal ID',
          description: 'Internal system ID',
          type: 'TEXT_FIELD',
          name: 'internalId',
          internal: true
        },
        {
          id: 3,
          displayName: 'Created Date',
          description: 'Record creation date',
          type: 'DATE_FIELD',
          name: 'createdDate',
          internal: false
        }
      ]
    };

    it('should fetch lead fields correctly', async () => {
      mockedApi.callAPI.mockResolvedValue(mockFieldsResponse);

      const result = await getEntityFields('lead');

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/entities/lead/fields?entityType=lead',
        headers: expect.any(Object),
        method: 'get'
      });

      // Should filter out internal fields and non-TEXT_FIELD types
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 1,
        displayName: 'First Name',
        description: 'Lead first name',
        type: 'TEXT_FIELD',
        name: 'firstName',
        internal: false
      });
    });

    it('should fetch contact fields correctly', async () => {
      mockedApi.callAPI.mockResolvedValue(mockFieldsResponse);

      const result = await getEntityFields('contact');

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/entities/contact/fields?entityType=contact',
        headers: expect.any(Object),
        method: 'get'
      });
    });

    it('should default to lead entity type', async () => {
      mockedApi.callAPI.mockResolvedValue(mockFieldsResponse);

      await getEntityFields();

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/entities/lead/fields?entityType=lead',
        headers: expect.any(Object),
        method: 'get'
      });
    });
  });

  describe('createChatbotStep1', () => {
    const mockStep1Data = {
      name: 'Test Chatbot',
      type: 'AI',
      description: 'Test Description',
      welcomeMessage: 'Welcome!',
      thankYouMessage: 'Thank you!',
      connectedAccountId: 6
    };

    it('should create chatbot with connected account ID', async () => {
      const mockResponse = {
        data: { id: 'chatbot-123', ...mockStep1Data },
        status: 201
      };
      mockedApi.callAPI.mockResolvedValue(mockResponse);

      const result = await createChatbotStep1(mockStep1Data);

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot',
        headers: expect.any(Object),
        method: 'post',
        data: mockStep1Data
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse.data);
    });
  });

  describe('submitChatbotQuestions', () => {
    const mockQuestions = [
      {
        question: 'What are your business hours?',
        fieldId: 1,
        fieldDisplayName: 'Business Hours'
      },
      {
        question: 'How can I contact support?',
        fieldId: 2,
        fieldDisplayName: 'Support Contact'
      }
    ];

    it('should submit questions with field mappings', async () => {
      const mockResponse = {
        data: { success: true },
        status: 200
      };
      mockedApi.callAPI.mockResolvedValue(mockResponse);

      const result = await submitChatbotQuestions('chatbot-123', mockQuestions);

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123/questions',
        headers: expect.any(Object),
        method: 'post',
        data: [
          {
            question: 'What are your business hours?',
            fieldId: 1,
            displayName: 'Business Hours'
          },
          {
            question: 'How can I contact support?',
            fieldId: 2,
            displayName: 'Support Contact'
          }
        ]
      });

      expect(result.success).toBe(true);
    });
  });

  describe('uploadKnowledgeBase', () => {
    it('should upload multiple files as FormData', async () => {
      const mockFiles = [
        new File(['content1'], 'doc1.pdf', { type: 'application/pdf' }),
        new File(['content2'], 'doc2.pdf', { type: 'application/pdf' })
      ];

      const mockResponse = {
        data: { knowledgebase_ids: ['kb1', 'kb2'] },
        status: 200
      };
      mockedApi.callAPI.mockResolvedValue(mockResponse);

      const result = await uploadKnowledgeBase('chatbot-123', mockFiles);

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123/knowledgebase',
        headers: expect.any(Object),
        method: 'post',
        data: expect.any(FormData)
      });

      expect(result.success).toBe(true);
    });
  });

  describe('publishChatbot', () => {
    it('should publish chatbot successfully', async () => {
      const mockResponse = {
        data: { status: 'published' },
        status: 200
      };
      mockedApi.callAPI.mockResolvedValue(mockResponse);

      const result = await publishChatbot('chatbot-123');

      expect(mockedApi.callAPI).toHaveBeenCalledWith({
        url: 'http://localhost:3000/v1/chatbot/chatbot-123/publish',
        headers: expect.any(Object),
        method: 'post'
      });

      expect(result.success).toBe(true);
    });
  });
});
