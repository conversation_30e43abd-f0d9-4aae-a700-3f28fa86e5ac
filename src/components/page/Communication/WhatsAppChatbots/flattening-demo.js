// Demo script to show how the flattening logic works with the provided API response
// This demonstrates that each entitiesToCreate item creates a separate dropdown row

const apiResponse = {
  "content": [
    {
      "id": 6,
      "wabaNumber": "+919028599212",
      "displayName": "Kalpesh QA Rename",
      "name": "Kalpesh QA",
      "entitiesToCreate": [],
      "status": "active",
      "isVerified": true
    },
    {
      "id": 5,
      "wabaNumber": "***********",
      "displayName": "Abhinav newq",
      "name": "Abhinav new",
      "entitiesToCreate": ["lead", "contact"],
      "status": "active",
      "isVerified": true
    },
    {
      "id": 4,
      "wabaNumber": "+************",
      "displayName": "New Wa Ba",
      "name": "Atharva New BusinessAccount",
      "entitiesToCreate": [],
      "status": "active",
      "isVerified": true
    },
    {
      "id": 3,
      "wabaNumber": "+************",
      "displayName": "support new name 786",
      "name": "<PERSON><PERSON><PERSON>",
      "entitiesToCreate": ["lead"],
      "status": "inactive",
      "isVerified": true
    },
    {
      "id": 7,
      "wabaNumber": "+**********",
      "displayName": "Multi Entity Account",
      "name": "Test Company",
      "entitiesToCreate": ["lead", "contact", "deal"],
      "status": "active",
      "isVerified": true
    }
  ]
};

// Simulate the flattening logic
function flattenConnectedAccounts(apiResponse) {
  const accounts = apiResponse.content || [];
  
  // Filter only active accounts
  const activeAccounts = accounts.filter(account => account.status === 'active');
  console.log(`\n📊 Found ${activeAccounts.length} active accounts out of ${accounts.length} total accounts\n`);
  
  // Flatten accounts based on entitiesToCreate
  const flattenedAccounts = [];
  
  activeAccounts.forEach((account, accountIndex) => {
    console.log(`🏢 Processing Account ${accountIndex + 1}:`);
    console.log(`   Name: ${account.displayName}`);
    console.log(`   Phone: ${account.wabaNumber}`);
    console.log(`   ID: ${account.id}`);
    console.log(`   entitiesToCreate: [${account.entitiesToCreate.join(', ')}]`);
    
    if (account.entitiesToCreate && account.entitiesToCreate.length > 0) {
      // Create one entry for each entity type - this creates multiple rows for same account
      account.entitiesToCreate.forEach((entityType, entityIndex) => {
        console.log(`   ✅ Creating dropdown row ${flattenedAccounts.length + 1} for entity: ${entityType.toUpperCase()}`);
        flattenedAccounts.push({
          id: account.id,
          wabaNumber: account.wabaNumber,
          displayName: account.displayName,
          name: account.name,
          entityType: entityType,
          status: account.status,
          isVerified: account.isVerified,
          originalAccount: account
        });
      });
    } else {
      // If no entitiesToCreate, default to 'lead'
      console.log(`   ✅ Creating dropdown row ${flattenedAccounts.length + 1} for default entity: LEAD`);
      flattenedAccounts.push({
        id: account.id,
        wabaNumber: account.wabaNumber,
        displayName: account.displayName,
        name: account.name,
        entityType: 'lead',
        status: account.status,
        isVerified: account.isVerified,
        originalAccount: account
      });
    }
    console.log(''); // Empty line for readability
  });
  
  return flattenedAccounts;
}

// Run the demo
console.log('🚀 WhatsApp Connected Accounts Flattening Demo');
console.log('================================================');

const flattened = flattenConnectedAccounts(apiResponse);

console.log(`\n📋 FINAL DROPDOWN OPTIONS (${flattened.length} total rows):`);
console.log('='.repeat(80));

flattened.forEach((option, index) => {
  console.log(`\n${index + 1}. "${option.displayName} (${option.wabaNumber}) - ${option.entityType.toUpperCase()}"`);
});

console.log('\n🎯 SUMMARY:');
console.log(`   • Total API accounts: ${apiResponse.content.length}`);
console.log(`   • Active accounts: ${apiResponse.content.filter(a => a.status === 'active').length}`);
console.log(`   • Dropdown rows created: ${flattened.length}`);
console.log(`   • Accounts with multiple entities: ${apiResponse.content.filter(a => a.entitiesToCreate.length > 1).length}`);

// Show which accounts created multiple rows
const multipleRowAccounts = apiResponse.content.filter(a => a.entitiesToCreate.length > 1);
if (multipleRowAccounts.length > 0) {
  console.log('\n📊 Accounts that created multiple dropdown rows:');
  multipleRowAccounts.forEach(account => {
    const rowCount = account.entitiesToCreate.length;
    console.log(`   • "${account.displayName}" → ${rowCount} rows (${account.entitiesToCreate.join(', ')})`);
  });
}

console.log('\n✅ Demo completed! Each entitiesToCreate item creates a separate dropdown row.');
