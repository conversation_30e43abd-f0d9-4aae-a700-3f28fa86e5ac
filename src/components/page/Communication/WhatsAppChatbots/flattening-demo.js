// Demo script to show how the flattening logic works with the provided API response
// This demonstrates that each entitiesToCreate item creates a separate dropdown row

const apiResponse = {
  "content": [
    {
      "id": 6,
      "wabaNumber": "+919028599212",
      "displayName": "Kalpesh QA Rename",
      "name": "Kalpesh QA",
      "entitiesToCreate": [],
      "status": "active",
      "isVerified": true,
      "onboardingStatus": "WABA_ONBOARDED"
    },
    {
      "id": 5,
      "wabaNumber": "***********",
      "displayName": "Abhinav newq",
      "name": "Abhinav new",
      "entitiesToCreate": ["lead", "contact"],
      "status": "inactive",
      "isVerified": true,
      "onboardingStatus": "REQUEST_SENT"
    },
    {
      "id": 4,
      "wabaNumber": "+************",
      "displayName": "New Wa Ba",
      "name": "Atharva New BusinessAccount",
      "entitiesToCreate": ["contact"],
      "status": "active",
      "isVerified": true,
      "onboardingStatus": "WABA_ONBOARDED"
    },
    {
      "id": 3,
      "wabaNumber": "+************",
      "displayName": "support new name 786",
      "name": "Kylas Shastri",
      "entitiesToCreate": ["lead"],
      "status": "inactive",
      "isVerified": true,
      "onboardingStatus": "REQUEST_SENT"
    }
  ]
};

// Simulate the flattening logic
function flattenConnectedAccounts(apiResponse) {
  const accounts = apiResponse.content || [];
  
  // Filter accounts that are both active AND have entitiesToCreate
  const validAccounts = accounts.filter(account =>
    account.status === 'active' &&
    account.entitiesToCreate &&
    account.entitiesToCreate.length > 0
  );
  console.log(`\n📊 Found ${validAccounts.length} valid accounts (active + non-empty entitiesToCreate) out of ${accounts.length} total accounts\n`);

  // Show which accounts were filtered out
  const filteredOut = accounts.filter(account =>
    account.status !== 'active' ||
    !account.entitiesToCreate ||
    account.entitiesToCreate.length === 0
  );

  if (filteredOut.length > 0) {
    console.log(`❌ Filtered out ${filteredOut.length} accounts:`);
    filteredOut.forEach(account => {
      const reasons = [];
      if (account.status !== 'active') reasons.push(`status: ${account.status}`);
      if (!account.entitiesToCreate || account.entitiesToCreate.length === 0) reasons.push('empty entitiesToCreate');
      console.log(`   • "${account.displayName}" - ${reasons.join(', ')}`);
    });
    console.log('');
  }
  
  // Flatten accounts based on entitiesToCreate
  const flattenedAccounts = [];
  
  validAccounts.forEach((account, accountIndex) => {
    console.log(`🏢 Processing Account ${accountIndex + 1}:`);
    console.log(`   Name: ${account.displayName}`);
    console.log(`   Phone: ${account.wabaNumber}`);
    console.log(`   ID: ${account.id}`);
    console.log(`   entitiesToCreate: [${account.entitiesToCreate.join(', ')}]`);
    
    // Create one entry for each entity type
    // Note: We already filtered out accounts with empty entitiesToCreate
    account.entitiesToCreate.forEach((entityType, entityIndex) => {
      console.log(`   ✅ Creating dropdown row ${flattenedAccounts.length + 1} for entity: ${entityType.toUpperCase()}`);
      flattenedAccounts.push({
        id: account.id,
        wabaNumber: account.wabaNumber,
        displayName: account.displayName,
        name: account.name,
        entityType: entityType,
        status: account.status,
        isVerified: account.isVerified,
        originalAccount: account
      });
    });
    console.log(''); // Empty line for readability
  });
  
  return flattenedAccounts;
}

// Run the demo
console.log('🚀 WhatsApp Connected Accounts Flattening Demo');
console.log('================================================');

const flattened = flattenConnectedAccounts(apiResponse);

console.log(`\n📋 FINAL DROPDOWN OPTIONS (${flattened.length} total rows):`);
console.log('='.repeat(80));

flattened.forEach((option, index) => {
  console.log(`\n${index + 1}. "${option.displayName} (${option.wabaNumber}) - ${option.entityType.toUpperCase()}"`);
});

console.log('\n🎯 SUMMARY:');
console.log(`   • Total API accounts: ${apiResponse.content.length}`);
console.log(`   • Active accounts: ${apiResponse.content.filter(a => a.status === 'active').length}`);
console.log(`   • Dropdown rows created: ${flattened.length}`);
console.log(`   • Accounts with multiple entities: ${apiResponse.content.filter(a => a.entitiesToCreate.length > 1).length}`);

// Show which accounts created multiple rows
const multipleRowAccounts = apiResponse.content.filter(a => a.entitiesToCreate.length > 1);
if (multipleRowAccounts.length > 0) {
  console.log('\n📊 Accounts that created multiple dropdown rows:');
  multipleRowAccounts.forEach(account => {
    const rowCount = account.entitiesToCreate.length;
    console.log(`   • "${account.displayName}" → ${rowCount} rows (${account.entitiesToCreate.join(', ')})`);
  });
}

console.log('\n✅ Demo completed! Each entitiesToCreate item creates a separate dropdown row.');
