// Demo script to show the new request body structure for POST /v1/chatbot

console.log('🚀 WhatsApp Chatbot Request Body Structure Demo');
console.log('='.repeat(60));

// Simulate user selecting a WhatsApp account from dropdown
const selectedWhatsAppAccount = {
  id: 4,
  wabaNumber: "+************",
  displayName: "New Wa Ba",
  name: "Atharva New BusinessAccount",
  entityType: "contact", // This comes from the flattened dropdown selection
  status: "active",
  isVerified: true
};

console.log('\n📋 User Selected WhatsApp Account:');
console.log(`   Display: "${selectedWhatsAppAccount.displayName} (${selectedWhatsAppAccount.wabaNumber}) - ${selectedWhatsAppAccount.entityType.toUpperCase()}"`);
console.log(`   Account ID: ${selectedWhatsAppAccount.id}`);
console.log(`   Entity Type: ${selectedWhatsAppAccount.entityType}`);

// Simulate form data from Step 1
const formData = {
  name: "Customer Support Bot",
  type: "AI",
  description: "AI-powered customer support chatbot",
  welcomeMessage: "Hello! How can I help you today?",
  thankYouMessage: "Thank you for contacting us!",
  whatsappAccount: selectedWhatsAppAccount
};

console.log('\n📝 Form Data from Step 1:');
console.log(`   Name: ${formData.name}`);
console.log(`   Type: ${formData.type}`);
console.log(`   Description: ${formData.description}`);
console.log(`   Welcome Message: ${formData.welcomeMessage}`);
console.log(`   Thank You Message: ${formData.thankYouMessage}`);

// OLD REQUEST BODY STRUCTURE (before changes)
const oldRequestBody = {
  name: formData.name,
  type: formData.type,
  description: formData.description,
  welcomeMessage: formData.welcomeMessage,
  thankYouMessage: formData.thankYouMessage,
  connectedAccountId: selectedWhatsAppAccount.id // Just the ID number
};

console.log('\n❌ OLD Request Body Structure:');
console.log(JSON.stringify(oldRequestBody, null, 2));

// NEW REQUEST BODY STRUCTURE (after changes)
const newRequestBody = {
  name: formData.name,
  type: formData.type,
  description: formData.description,
  welcomeMessage: formData.welcomeMessage,
  thankYouMessage: formData.thankYouMessage,
  connectedAccountId: {
    id: selectedWhatsAppAccount.id,
    displayName: selectedWhatsAppAccount.displayName,
    entityType: selectedWhatsAppAccount.entityType.toUpperCase()
  }
};

console.log('\n✅ NEW Request Body Structure:');
console.log(JSON.stringify(newRequestBody, null, 2));

console.log('\n🔄 Step 2 Dynamic Field Loading:');
console.log('='.repeat(40));

// Simulate the API response from Step 1 (chatbot creation)
const chatbotApiResponse = {
  id: "chatbot-12345",
  ...newRequestBody,
  connected_account_id: newRequestBody.connectedAccountId, // API uses snake_case
  questions: [],
  isActive: false,
  createdAt: new Date().toISOString()
};

console.log('\n📦 Chatbot Created (API Response):');
console.log(`   Chatbot ID: ${chatbotApiResponse.id}`);
console.log(`   Connected Account: ${JSON.stringify(chatbotApiResponse.connected_account_id, null, 2)}`);

// Step 2: Dynamic field loading based on entity type
const entityType = chatbotApiResponse.connected_account_id.entityType.toLowerCase();
console.log(`\n🎯 Step 2 Field Loading Logic:`);
console.log(`   Entity Type from Chatbot: ${entityType}`);

if (entityType === 'lead') {
  console.log(`   📞 API Call: GET /v1/entities/lead/fields?entityType=lead`);
  console.log(`   📋 Loading LEAD fields for question mapping`);
} else if (entityType === 'contact') {
  console.log(`   📞 API Call: GET /v1/entities/contact/fields?entityType=contact`);
  console.log(`   📋 Loading CONTACT fields for question mapping`);
} else {
  console.log(`   ⚠️  Unknown entity type: ${entityType}`);
}

console.log('\n🎯 SUMMARY:');
console.log('='.repeat(30));
console.log('✅ Task 1 Complete: Updated POST /v1/chatbot request body');
console.log('   • connectedAccountId is now an object with id, displayName, entityType');
console.log('   • Preserves all account information for later use');
console.log('');
console.log('✅ Task 2 Complete: Dynamic field loading in Step 2');
console.log('   • Uses connectedAccountId.entityType from chatbot data');
console.log('   • Calls appropriate API endpoint based on entity type');
console.log('   • LEAD → /v1/entities/lead/fields?entityType=lead');
console.log('   • CONTACT → /v1/entities/contact/fields?entityType=contact');

console.log('\n🔧 Implementation Details:');
console.log('• Form submission creates connectedAccountId object from selected dropdown');
console.log('• Step 1 → Step 2 transition triggers field loading based on entity type');
console.log('• Field dropdown populated with TEXT_FIELD type fields only');
console.log('• Maintains backward compatibility with existing field mapping logic');
