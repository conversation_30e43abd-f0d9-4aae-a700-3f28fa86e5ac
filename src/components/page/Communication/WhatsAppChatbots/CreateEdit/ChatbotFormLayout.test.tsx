import * as React from 'react';
import { shallow } from 'enzyme';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import ChatbotFormLayout from './ChatbotFormLayout';
import { EntityFormAction } from '../../../formLayout/models/Form';
import { ChatbotType } from '../models';

// Mock the service functions
jest.mock('../service', () => ({
  createChatbotStep1: jest.fn(),
  submitChatbotQuestions: jest.fn(),
  uploadKnowledgeBase: jest.fn(),
  publishChatbot: jest.fn(),
  getEntityFields: jest.fn(),
  getConnectedAccounts: jest.fn()
}));

// Mock the API middleware
jest.mock('../../../../../middlewares/api', () => ({
  showSuccessToast: jest.fn(),
  showErrorToast: jest.fn()
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useHistory: () => ({
    push: jest.fn()
  }),
  withRouter: (component: any) => component
}));

const mockStore = configureStore([]);

// Create a minimal mock store with required state
const createMockStore = () => {
  return mockStore({
    appData: {
      profilePermissions: [],
      profile: {
        id: 1,
        name: 'Test User'
      },
      tasksMetaData: {
        idNameStore: {
          assignedTo: {}
        }
      }
    },
    form: {
      chatbotForm: {
        values: {
          name: 'Test Chatbot',
          description: 'Test Description',
          type: ChatbotType.AI,
          welcomeMessage: 'Welcome!',
          thankYouMessage: 'Thank you!',
          questions: []
        }
      }
    }
  });
};

describe('ChatbotFormLayout', () => {
  const defaultProps = {
    mode: EntityFormAction.CREATE,
    match: { params: {} },
    history: { push: jest.fn() },
    location: { pathname: '/create' }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render without crashing', () => {
    const store = createMockStore();
    const component = shallow(
      <Provider store={store}>
        <BrowserRouter>
          <ChatbotFormLayout {...defaultProps} />
        </BrowserRouter>
      </Provider>
    );
    expect(component.length).toBe(1);
  });

  it('should have correct initial state', () => {
    const store = createMockStore();
    const component = shallow(
      <Provider store={store}>
        <BrowserRouter>
          <ChatbotFormLayout {...defaultProps} />
        </BrowserRouter>
      </Provider>
    );
    expect(component.exists()).toBe(true);
  });

  describe('Knowledge Base Multiple File Upload', () => {
    // Mock File constructor
    const createMockFile = (name: string, size: number, type: string = 'application/pdf') => {
      const file = new File([''], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('should handle multiple PDF file selection', () => {
      const file1 = createMockFile('document1.pdf', 1024 * 1024); // 1MB
      const file2 = createMockFile('document2.pdf', 2 * 1024 * 1024); // 2MB

      // Test that multiple files can be selected
      expect(file1.name).toBe('document1.pdf');
      expect(file2.name).toBe('document2.pdf');
      expect(file1.size).toBe(1024 * 1024);
      expect(file2.size).toBe(2 * 1024 * 1024);
    });

    it('should reject non-PDF files', () => {
      const txtFile = createMockFile('document.txt', 1024, 'text/plain');
      const docFile = createMockFile('document.doc', 1024, 'application/msword');

      // Test file type validation
      expect(txtFile.type).toBe('text/plain');
      expect(docFile.type).toBe('application/msword');
      expect(txtFile.name.endsWith('.pdf')).toBe(false);
      expect(docFile.name.endsWith('.pdf')).toBe(false);
    });

    it('should reject files larger than 25MB', () => {
      const largeFile = createMockFile('large-document.pdf', 26 * 1024 * 1024); // 26MB

      // Test file size validation
      expect(largeFile.size / (1024 * 1024)).toBeGreaterThan(25);
    });

    it('should handle duplicate file detection', () => {
      const file1 = createMockFile('document.pdf', 1024 * 1024);
      const file2 = createMockFile('document.pdf', 1024 * 1024); // Same name and size

      // Test duplicate detection logic
      const isDuplicate = file1.name === file2.name && file1.size === file2.size;
      expect(isDuplicate).toBe(true);
    });

    it('should support file removal functionality', () => {
      const files = [
        createMockFile('doc1.pdf', 1024),
        createMockFile('doc2.pdf', 2048),
        createMockFile('doc3.pdf', 3072)
      ];

      // Test individual file removal
      const filesAfterRemoval = files.filter((_, index) => index !== 1);
      expect(filesAfterRemoval.length).toBe(2);
      expect(filesAfterRemoval[0].name).toBe('doc1.pdf');
      expect(filesAfterRemoval[1].name).toBe('doc3.pdf');
    });

    it('should support remove all files functionality', () => {
      const files = [
        createMockFile('doc1.pdf', 1024),
        createMockFile('doc2.pdf', 2048)
      ];

      // Test remove all functionality
      const emptyFiles: File[] = [];
      expect(emptyFiles.length).toBe(0);
    });

    it('should display correct file count and size information', () => {
      const files = [
        createMockFile('doc1.pdf', 1024 * 1024), // 1MB
        createMockFile('doc2.pdf', 2 * 1024 * 1024) // 2MB
      ];

      // Test file count display
      expect(files.length).toBe(2);

      // Test file size formatting
      const file1SizeMB = (files[0].size / (1024 * 1024)).toFixed(2);
      const file2SizeMB = (files[1].size / (1024 * 1024)).toFixed(2);
      expect(file1SizeMB).toBe('1.00');
      expect(file2SizeMB).toBe('2.00');
    });

    it('should handle API endpoint for knowledge base upload', () => {
      const chatbotId = 'test-chatbot-123';
      const expectedEndpoint = `/v1/chatbot/${chatbotId}/knowledgebase`;

      // Test API endpoint construction
      expect(expectedEndpoint).toBe('/v1/chatbot/test-chatbot-123/knowledgebase');
    });
  });

  describe('Connected Accounts Integration', () => {
    const mockConnectedAccounts = [
      {
        id: 6,
        wabaNumber: "+************",
        displayName: "Kalpesh QA Rename",
        name: "Kalpesh QA",
        entitiesToCreate: [],
        status: "active",
        isVerified: true
      },
      {
        id: 4,
        wabaNumber: "+************",
        displayName: "New Wa Ba",
        name: "Atharva New BusinessAccount",
        entitiesToCreate: [],
        status: "active",
        isVerified: true
      },
      {
        id: 5,
        wabaNumber: "***********",
        displayName: "Abhinav newq",
        name: "Abhinav new",
        entitiesToCreate: ["lead", "contact"],
        status: "active",
        isVerified: true
      }
    ];

    const mockFlattenedAccounts = [
      {
        id: 6,
        wabaNumber: "+************",
        displayName: "Kalpesh QA Rename",
        name: "Kalpesh QA",
        entityType: "lead",
        status: "active",
        isVerified: true,
        originalAccount: mockConnectedAccounts[0]
      },
      {
        id: 4,
        wabaNumber: "+************",
        displayName: "New Wa Ba",
        name: "Atharva New BusinessAccount",
        entityType: "lead",
        status: "active",
        isVerified: true,
        originalAccount: mockConnectedAccounts[1]
      },
      {
        id: 5,
        wabaNumber: "***********",
        displayName: "Abhinav newq",
        name: "Abhinav new",
        entityType: "lead",
        status: "active",
        isVerified: true,
        originalAccount: mockConnectedAccounts[2]
      },
      {
        id: 5,
        wabaNumber: "***********",
        displayName: "Abhinav newq",
        name: "Abhinav new",
        entityType: "contact",
        status: "active",
        isVerified: true,
        originalAccount: mockConnectedAccounts[2]
      }
    ];

    it('should filter only active connected accounts', () => {
      const allAccounts = [
        ...mockConnectedAccounts,
        {
          id: 3,
          wabaNumber: "+************",
          displayName: "support new name 786",
          name: "Kylas Shastri",
          entitiesToCreate: ["lead"],
          status: "inactive",
          isVerified: true
        }
      ];

      const activeAccounts = allAccounts.filter(account => account.status === 'active');
      expect(activeAccounts.length).toBe(3);
      expect(activeAccounts.every(account => account.status === 'active')).toBe(true);
    });

    it('should flatten accounts based on entitiesToCreate field', () => {
      const account = mockConnectedAccounts[2]; // Has both lead and contact
      const flattened = [];

      if (account.entitiesToCreate && account.entitiesToCreate.length > 0) {
        account.entitiesToCreate.forEach(entityType => {
          flattened.push({
            id: account.id,
            wabaNumber: account.wabaNumber,
            displayName: account.displayName,
            name: account.name,
            entityType: entityType,
            status: account.status,
            isVerified: account.isVerified,
            originalAccount: account
          });
        });
      }

      expect(flattened.length).toBe(2);
      expect(flattened[0].entityType).toBe('lead');
      expect(flattened[1].entityType).toBe('contact');
    });

    it('should default to lead entity when entitiesToCreate is empty', () => {
      const account = mockConnectedAccounts[0]; // Has empty entitiesToCreate
      const flattened = [];

      if (account.entitiesToCreate && account.entitiesToCreate.length > 0) {
        account.entitiesToCreate.forEach(entityType => {
          flattened.push({
            id: account.id,
            entityType: entityType,
            originalAccount: account
          });
        });
      } else {
        // Default to 'lead'
        flattened.push({
          id: account.id,
          entityType: 'lead',
          originalAccount: account
        });
      }

      expect(flattened.length).toBe(1);
      expect(flattened[0].entityType).toBe('lead');
    });

    it('should format dropdown options with exact required format', () => {
      const account = mockFlattenedAccounts[0];
      const dropdownOption = {
        id: `${account.id}-${account.entityType}-0`,
        displayName: `${account.displayName} (${account.wabaNumber}) - ${account.entityType.toUpperCase()}`,
        wabaNumber: account.wabaNumber,
        entityType: account.entityType,
        accountId: account.id,
        accountName: account.name,
        isVerified: account.isVerified,
        originalAccount: account
      };

      // Test exact format: "displayName (wabaNumber) - ENTITYTYPE"
      expect(dropdownOption.displayName).toBe('Kalpesh QA Rename (+************) - LEAD');

      // Verify the format matches the pattern exactly
      const expectedPattern = /^.+ \(.+\) - [A-Z]+$/;
      expect(dropdownOption.displayName).toMatch(expectedPattern);
    });

    it('should create exact format for user provided example', () => {
      // User's example: {"id": 5, "displayName": "Abhinav newq", "wabaNumber": "***********", "entitiesToCreate": ["lead", "contact"], "status": true}
      const userAccount = {
        id: 5,
        displayName: "Abhinav newq",
        wabaNumber: "***********",
        entitiesToCreate: ["lead", "contact"],
        status: "active",
        isVerified: true
      };

      // Simulate flattening for this account
      const flattenedOptions = [];
      userAccount.entitiesToCreate.forEach((entityType, index) => {
        flattenedOptions.push({
          id: `${userAccount.id}-${entityType}-${index}`,
          displayName: `${userAccount.displayName} (${userAccount.wabaNumber}) - ${entityType.toUpperCase()}`,
          entityType: entityType,
          originalAccount: userAccount
        });
      });

      // Should create exactly 2 dropdown options
      expect(flattenedOptions).toHaveLength(2);

      // Check exact format matches user's expectation
      expect(flattenedOptions[0].displayName).toBe('Abhinav newq (***********) - LEAD');
      expect(flattenedOptions[1].displayName).toBe('Abhinav newq (***********) - CONTACT');
    });

    it('should handle undefined entityType gracefully', () => {
      // Test case that could cause toUpperCase error
      const accountWithUndefinedEntityType = {
        id: 5,
        displayName: "Test Account",
        wabaNumber: "***********",
        entityType: undefined // This could cause the error
      };

      // Simulate the fixed logic
      const connectedAccountId = accountWithUndefinedEntityType ? {
        id: accountWithUndefinedEntityType.id,
        displayName: accountWithUndefinedEntityType.displayName || '',
        entityType: (accountWithUndefinedEntityType.entityType || 'lead').toUpperCase()
      } : null;

      expect(connectedAccountId).toEqual({
        id: 5,
        displayName: "Test Account",
        entityType: "LEAD" // Should default to LEAD
      });
    });

    it('should handle null entityType gracefully', () => {
      // Test case that could cause toUpperCase error
      const accountWithNullEntityType = {
        id: 5,
        displayName: "Test Account",
        wabaNumber: "***********",
        entityType: null // This could cause the error
      };

      // Simulate the fixed logic
      const connectedAccountId = accountWithNullEntityType ? {
        id: accountWithNullEntityType.id,
        displayName: accountWithNullEntityType.displayName || '',
        entityType: (accountWithNullEntityType.entityType || 'lead').toUpperCase()
      } : null;

      expect(connectedAccountId).toEqual({
        id: 5,
        displayName: "Test Account",
        entityType: "LEAD" // Should default to LEAD
      });
    });

    it('should create multiple dropdown options for account with multiple entity types', () => {
      // Account with multiple entity types
      const accountWithMultipleEntities = {
        id: 5,
        wabaNumber: "***********",
        displayName: "Abhinav newq",
        name: "Abhinav new",
        entitiesToCreate: ["lead", "contact", "deal"],
        status: "active",
        isVerified: true
      };

      // Simulate flattening
      const flattenedOptions = [];
      accountWithMultipleEntities.entitiesToCreate.forEach((entityType, index) => {
        flattenedOptions.push({
          id: `${accountWithMultipleEntities.id}-${entityType}-${index}`,
          displayName: `${accountWithMultipleEntities.displayName} (${accountWithMultipleEntities.wabaNumber}) - ${entityType.toUpperCase()}`,
          wabaNumber: accountWithMultipleEntities.wabaNumber,
          entityType: entityType,
          accountId: accountWithMultipleEntities.id,
          accountName: accountWithMultipleEntities.name,
          isVerified: accountWithMultipleEntities.isVerified,
          originalAccount: accountWithMultipleEntities
        });
      });

      // Should create 3 separate dropdown options
      expect(flattenedOptions).toHaveLength(3);

      // All should have same account info but different entity types
      expect(flattenedOptions[0].id).toBe('5-lead-0');
      expect(flattenedOptions[1].id).toBe('5-contact-1');
      expect(flattenedOptions[2].id).toBe('5-deal-2');

      expect(flattenedOptions[0].entityType).toBe('lead');
      expect(flattenedOptions[1].entityType).toBe('contact');
      expect(flattenedOptions[2].entityType).toBe('deal');

      // All should have same account details
      flattenedOptions.forEach(option => {
        expect(option.accountId).toBe(5);
        expect(option.accountName).toBe('Abhinav new');
        expect(option.wabaNumber).toBe('***********');
        expect(option.displayName).toContain('Abhinav newq (***********)');
      });
    });
  });

  describe('Dynamic Entity Fields Loading', () => {
    const mockLeadFields = [
      {
        id: 1,
        displayName: 'First Name',
        description: 'Lead first name',
        type: 'TEXT_FIELD',
        name: 'firstName',
        internal: false
      },
      {
        id: 2,
        displayName: 'Email',
        description: 'Lead email address',
        type: 'TEXT_FIELD',
        name: 'email',
        internal: false
      }
    ];

    const mockContactFields = [
      {
        id: 3,
        displayName: 'Contact Name',
        description: 'Contact full name',
        type: 'TEXT_FIELD',
        name: 'contactName',
        internal: false
      },
      {
        id: 4,
        displayName: 'Phone',
        description: 'Contact phone number',
        type: 'TEXT_FIELD',
        name: 'phone',
        internal: false
      }
    ];

    it('should load lead fields when lead entity is selected', () => {
      const entityType = 'lead';
      const expectedEndpoint = `/v1/entities/${entityType}/fields?entityType=${entityType}`;

      expect(expectedEndpoint).toBe('/v1/entities/lead/fields?entityType=lead');
    });

    it('should load contact fields when contact entity is selected', () => {
      const entityType = 'contact';
      const expectedEndpoint = `/v1/entities/${entityType}/fields?entityType=${entityType}`;

      expect(expectedEndpoint).toBe('/v1/entities/contact/fields?entityType=contact');
    });

    it('should filter out internal fields', () => {
      const allFields = [
        ...mockLeadFields,
        {
          id: 5,
          displayName: 'Internal ID',
          description: 'Internal system ID',
          type: 'TEXT_FIELD',
          name: 'internalId',
          internal: true
        }
      ];

      const externalFields = allFields.filter(field => !field.internal && field.type === 'TEXT_FIELD');
      expect(externalFields.length).toBe(2);
      expect(externalFields.every(field => !field.internal)).toBe(true);
    });

    it('should only include TEXT_FIELD type fields', () => {
      const allFields = [
        ...mockLeadFields,
        {
          id: 6,
          displayName: 'Created Date',
          description: 'Record creation date',
          type: 'DATE_FIELD',
          name: 'createdDate',
          internal: false
        }
      ];

      const textFields = allFields.filter(field => !field.internal && field.type === 'TEXT_FIELD');
      expect(textFields.length).toBe(2);
      expect(textFields.every(field => field.type === 'TEXT_FIELD')).toBe(true);
    });
  });
});
