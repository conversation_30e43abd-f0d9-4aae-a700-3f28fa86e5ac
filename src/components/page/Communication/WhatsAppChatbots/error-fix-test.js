// Test script to verify the toUpperCase error is fixed

console.log('🔧 Testing toUpperCase Error Fix');
console.log('='.repeat(40));

// Test cases that could cause the error
const testCases = [
  {
    name: 'Valid WhatsApp Account',
    whatsappAccount: {
      id: 4,
      displayName: 'Test Account',
      entityType: 'contact'
    }
  },
  {
    name: 'WhatsApp Account with undefined entityType',
    whatsappAccount: {
      id: 4,
      displayName: 'Test Account',
      entityType: undefined
    }
  },
  {
    name: 'WhatsApp Account with null entityType',
    whatsappAccount: {
      id: 4,
      displayName: 'Test Account',
      entityType: null
    }
  },
  {
    name: 'WhatsApp Account with empty string entityType',
    whatsappAccount: {
      id: 4,
      displayName: 'Test Account',
      entityType: ''
    }
  },
  {
    name: 'No WhatsApp Account (null)',
    whatsappAccount: null
  },
  {
    name: 'No WhatsApp Account (undefined)',
    whatsappAccount: undefined
  }
];

// Simulate the fixed logic
function createConnectedAccountId(whatsappAccount) {
  return whatsappAccount ? {
    id: whatsappAccount.id,
    displayName: whatsappAccount.displayName || '',
    entityType: (whatsappAccount.entityType || 'lead').toUpperCase()
  } : null;
}

// Test each case
testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. Testing: ${testCase.name}`);
  console.log(`   Input: ${JSON.stringify(testCase.whatsappAccount)}`);
  
  try {
    const result = createConnectedAccountId(testCase.whatsappAccount);
    console.log(`   ✅ Result: ${JSON.stringify(result)}`);
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }
});

console.log('\n🎯 Test Results Summary:');
console.log('='.repeat(30));

// Test the old logic (that would cause errors)
function oldCreateConnectedAccountId(whatsappAccount) {
  return whatsappAccount ? {
    id: whatsappAccount.id,
    displayName: whatsappAccount.displayName,
    entityType: whatsappAccount.entityType.toUpperCase() // This would fail!
  } : null;
}

console.log('\n❌ OLD Logic (would cause errors):');
const problematicCases = testCases.slice(1, 4); // Cases with undefined/null/empty entityType

problematicCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. Testing OLD logic with: ${testCase.name}`);
  try {
    const result = oldCreateConnectedAccountId(testCase.whatsappAccount);
    console.log(`   Unexpected success: ${JSON.stringify(result)}`);
  } catch (error) {
    console.log(`   ❌ Expected Error: ${error.message}`);
  }
});

console.log('\n✅ NEW Logic (handles all cases safely):');
problematicCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. Testing NEW logic with: ${testCase.name}`);
  try {
    const result = createConnectedAccountId(testCase.whatsappAccount);
    console.log(`   ✅ Safe Result: ${JSON.stringify(result)}`);
  } catch (error) {
    console.log(`   ❌ Unexpected Error: ${error.message}`);
  }
});

console.log('\n🔧 Fix Applied:');
console.log('• Added fallback: (entityType || "lead").toUpperCase()');
console.log('• Added null check: displayName || ""');
console.log('• Handles undefined, null, and empty string entityType values');
console.log('• Defaults to "lead" entity type when entityType is missing');

console.log('\n✅ Error Fix Complete!');
console.log('The toUpperCase error should no longer occur.');
