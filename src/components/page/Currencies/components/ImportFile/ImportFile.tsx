import * as React from 'react';
import classnames from 'classnames';

import { ImportStatus } from '../../model';
import { isBlank } from '../../../../../utils/globalUtil';

interface Props {
  status: string;
  formData: FormData;
  handleChange: (val: FormData) => void;
}

const ImportFile:React.FC<Props> = ({ status, formData, handleChange }) => {
const ImportFile:React.FC<Props> = ({ status, formData, handleChange }) => {
  const [error, setError] = React.useState<string>(null);

  const onFileSelect = (file) => {
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      setError('File format not supported, please upload .csv file');
      return;
    }

    if (file.size / 1048576 >= 25) {
      setError('File size can not be greater than 25MB');
      return;
    }

    const payload = new FormData();
    payload.append('file', file);
    payload.append('name', file.name);
    payload.append('size', file.size);

    setError(null);
    handleChange(payload);
  };

  const onFileRemove = () => {
    setError(null);
    handleChange(null);
  };

  const isFileUploaded = () => !isBlank(formData?.get('name'));

  return (
    <div>
      <div className={classnames('form-control select-file w-100', { 'is-invalid' : !isBlank(error) }, { 'upload-file': isFileUploaded() })}>
        { !isFileUploaded() &&
          <span className="select-file-placeholder d-inline-block">
            { status === ImportStatus.COMPLETED ? 'Select a file to upload' : 'Select Edited File to Re-Upload' }
          </span>}

        { isFileUploaded() &&
          <div className="selected-file d-inline-block">
            <span className="mr-3">
              {(formData.get('name') as string).substring(0, 85)}{(formData.get('name') as string).length >= 85 ? '...' : ''}
            </span>
            <span className="cancel-file-wrapper" onClick={onFileRemove} >
              <i className="fas fa-times"/>
            </span>
          </div>
        }

        <input
          type="file"
          accept=".csv"
          id="selectFile"
          name="file_to_upload"
          onChange={(e) => {
            onFileSelect(e.target.files[0]);
            e.target.value = null;
          }}
        />

        <button
          type="button"
          disabled={isFileUploaded()}
          className="btn btn-primary select-file-button"
          onClick={() => document.getElementById('selectFile')?.click()}
        >
          Select File
        </button>
      </div>

      { error && <span className="invalid-feedback d-flex">{error}</span> }
    </div>
  );
};

export default ImportFile;
